#!/usr/bin/env python3
"""
GitHub Project Uploader Tool
أداة لرفع المشاريع على GitHub بشكل تلقائي

المميزات:
- إنشاء مستودع جديد على GitHub
- رفع الملفات والمجلدات
- إعداد README.md تلقائياً
- دعم ملفات .gitignore
- إدارة الفروع
- دعم المشاريع المختلفة (Python, JavaScript, etc.)
"""

import os
import sys
import json
import subprocess
import requests
from pathlib import Path
from typing import Optional, Dict, List
import argparse
from datetime import datetime

class GitHubUploader:
    def __init__(self, token: str):
        """
        تهيئة أداة رفع GitHub
        
        Args:
            token: GitHub Personal Access Token
        """
        self.token = token
        self.headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        self.base_url = 'https://api.github.com'
        
    def create_repository(self, name: str, description: str = "", 
                         private: bool = False, auto_init: bool = False) -> Dict:
        """
        إنشاء مستودع جديد على GitHub
        
        Args:
            name: اسم المستودع
            description: وصف المستودع
            private: هل المستودع خاص
            auto_init: إنشاء README تلقائياً
            
        Returns:
            معلومات المستودع المُنشأ
        """
        data = {
            'name': name,
            'description': description,
            'private': private,
            'auto_init': auto_init
        }
        
        response = requests.post(
            f'{self.base_url}/user/repos',
            headers=self.headers,
            json=data
        )
        
        if response.status_code == 201:
            print(f"✅ تم إنشاء المستودع بنجاح: {name}")
            return response.json()
        else:
            print(f"❌ فشل في إنشاء المستودع: {response.json()}")
            return {}
    
    def detect_project_type(self, project_path: str) -> str:
        """
        تحديد نوع المشروع تلقائياً
        
        Args:
            project_path: مسار المشروع
            
        Returns:
            نوع المشروع
        """
        path = Path(project_path)
        
        # فحص ملفات Python
        if any(path.glob("*.py")) or (path / "requirements.txt").exists():
            return "python"
        
        # فحص ملفات JavaScript/Node.js
        if (path / "package.json").exists() or any(path.glob("*.js")):
            return "javascript"
        
        # فحص ملفات Java
        if any(path.glob("*.java")) or (path / "pom.xml").exists():
            return "java"
        
        # فحص ملفات C++
        if any(path.glob("*.cpp")) or any(path.glob("*.hpp")):
            return "cpp"
        
        # فحص ملفات C#
        if any(path.glob("*.cs")) or any(path.glob("*.csproj")):
            return "csharp"
        
        # فحص ملفات Go
        if any(path.glob("*.go")) or (path / "go.mod").exists():
            return "go"
        
        # فحص ملفات Rust
        if (path / "Cargo.toml").exists():
            return "rust"
        
        return "general"
    
    def generate_gitignore(self, project_type: str) -> str:
        """
        إنشاء ملف .gitignore حسب نوع المشروع
        
        Args:
            project_type: نوع المشروع
            
        Returns:
            محتوى ملف .gitignore
        """
        gitignore_templates = {
            "python": """
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
""",
            "javascript": """
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless
""",
            "general": """
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log

# Temporary files
*.tmp
*.temp

# Build outputs
build/
dist/
out/
"""
        }
        
        return gitignore_templates.get(project_type, gitignore_templates["general"])

    def generate_readme(self, project_name: str, project_type: str,
                       description: str = "") -> str:
        """
        إنشاء ملف README.md تلقائياً

        Args:
            project_name: اسم المشروع
            project_type: نوع المشروع
            description: وصف المشروع

        Returns:
            محتوى ملف README.md
        """
        current_date = datetime.now().strftime("%Y-%m-%d")

        readme_content = f"""# {project_name}

{description if description else f"مشروع {project_type} تم إنشاؤه في {current_date}"}

## الوصف

هذا المشروع تم إنشاؤه باستخدام أداة GitHub Uploader.

## التثبيت

"""

        # إضافة تعليمات التثبيت حسب نوع المشروع
        if project_type == "python":
            readme_content += """```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل المشروع
python main.py
```

## المتطلبات

- Python 3.6+
- pip

"""
        elif project_type == "javascript":
            readme_content += """```bash
# تثبيت المتطلبات
npm install

# تشغيل المشروع
npm start
```

## المتطلبات

- Node.js
- npm

"""
        elif project_type == "java":
            readme_content += """```bash
# تجميع المشروع
javac *.java

# تشغيل المشروع
java Main
```

## المتطلبات

- Java JDK 8+

"""
        else:
            readme_content += """```bash
# اتبع التعليمات الخاصة بنوع مشروعك
```

"""

        readme_content += f"""## الاستخدام

اكتب هنا كيفية استخدام المشروع.

## المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

اسم المطور - [@your_username](https://twitter.com/your_username) - <EMAIL>

رابط المشروع: [https://github.com/yourusername/{project_name}](https://github.com/yourusername/{project_name})

---
تم إنشاء هذا المشروع باستخدام [GitHub Uploader Tool](https://github.com/yourusername/github-uploader)
"""

        return readme_content

    def init_git_repo(self, project_path: str) -> bool:
        """
        تهيئة مستودع Git محلي

        Args:
            project_path: مسار المشروع

        Returns:
            True إذا تم بنجاح، False إذا فشل
        """
        try:
            os.chdir(project_path)

            # تهيئة Git
            subprocess.run(['git', 'init'], check=True, capture_output=True)
            print("✅ تم تهيئة مستودع Git محلي")

            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تهيئة Git: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False

    def add_remote_origin(self, repo_url: str) -> bool:
        """
        إضافة remote origin للمستودع

        Args:
            repo_url: رابط المستودع على GitHub

        Returns:
            True إذا تم بنجاح، False إذا فشل
        """
        try:
            subprocess.run(['git', 'remote', 'add', 'origin', repo_url],
                         check=True, capture_output=True)
            print("✅ تم إضافة remote origin")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في إضافة remote origin: {e}")
            return False

    def commit_and_push(self, commit_message: str = "Initial commit") -> bool:
        """
        إضافة الملفات وعمل commit و push

        Args:
            commit_message: رسالة الـ commit

        Returns:
            True إذا تم بنجاح، False إذا فشل
        """
        try:
            # إضافة جميع الملفات
            subprocess.run(['git', 'add', '.'], check=True, capture_output=True)
            print("✅ تم إضافة الملفات")

            # عمل commit
            subprocess.run(['git', 'commit', '-m', commit_message],
                         check=True, capture_output=True)
            print("✅ تم عمل commit")

            # تحديد الفرع الرئيسي
            subprocess.run(['git', 'branch', '-M', 'main'],
                         check=True, capture_output=True)

            # Push للمستودع
            subprocess.run(['git', 'push', '-u', 'origin', 'main'],
                         check=True, capture_output=True)
            print("✅ تم رفع المشروع على GitHub")

            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في رفع المشروع: {e}")
            return False

    def upload_project(self, project_path: str, repo_name: str,
                      description: str = "", private: bool = False,
                      commit_message: str = "Initial commit") -> bool:
        """
        رفع مشروع كامل على GitHub

        Args:
            project_path: مسار المشروع المحلي
            repo_name: اسم المستودع على GitHub
            description: وصف المستودع
            private: هل المستودع خاص
            commit_message: رسالة الـ commit

        Returns:
            True إذا تم بنجاح، False إذا فشل
        """
        print(f"🚀 بدء رفع المشروع: {repo_name}")

        # التحقق من وجود المجلد
        if not os.path.exists(project_path):
            print(f"❌ المجلد غير موجود: {project_path}")
            return False

        # تحديد نوع المشروع
        project_type = self.detect_project_type(project_path)
        print(f"📁 نوع المشروع: {project_type}")

        # إنشاء المستودع على GitHub
        repo_data = self.create_repository(repo_name, description, private)
        if not repo_data:
            return False

        # الانتقال لمجلد المشروع
        original_dir = os.getcwd()
        os.chdir(project_path)

        try:
            # إنشاء ملف .gitignore إذا لم يكن موجوداً
            gitignore_path = Path(project_path) / '.gitignore'
            if not gitignore_path.exists():
                gitignore_content = self.generate_gitignore(project_type)
                with open(gitignore_path, 'w', encoding='utf-8') as f:
                    f.write(gitignore_content)
                print("✅ تم إنشاء ملف .gitignore")

            # إنشاء ملف README.md إذا لم يكن موجوداً
            readme_path = Path(project_path) / 'README.md'
            if not readme_path.exists():
                readme_content = self.generate_readme(repo_name, project_type, description)
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                print("✅ تم إنشاء ملف README.md")

            # تهيئة Git
            if not self.init_git_repo(project_path):
                return False

            # إضافة remote origin
            repo_url = repo_data['clone_url']
            if not self.add_remote_origin(repo_url):
                return False

            # رفع المشروع
            if not self.commit_and_push(commit_message):
                return False

            print(f"🎉 تم رفع المشروع بنجاح!")
            print(f"🔗 رابط المستودع: {repo_data['html_url']}")

            return True

        except Exception as e:
            print(f"❌ خطأ أثناء رفع المشروع: {e}")
            return False
        finally:
            # العودة للمجلد الأصلي
            os.chdir(original_dir)


def main():
    """الواجهة الرئيسية للأداة"""
    print("=" * 50)
    print("🚀 أداة رفع المشاريع على GitHub")
    print("=" * 50)

    # طلب GitHub Token
    token = input("أدخل GitHub Personal Access Token: ").strip()
    if not token:
        print("❌ يجب إدخال GitHub Token")
        return

    # إنشاء كائن الأداة
    uploader = GitHubUploader(token)

    while True:
        print("\n" + "=" * 30)
        print("اختر العملية:")
        print("1. رفع مشروع جديد")
        print("2. إنشاء مستودع فقط")
        print("3. خروج")
        print("=" * 30)

        choice = input("اختيارك (1-3): ").strip()

        if choice == "1":
            # رفع مشروع كامل
            project_path = input("أدخل مسار المشروع: ").strip()
            if not project_path:
                print("❌ يجب إدخال مسار المشروع")
                continue

            repo_name = input("أدخل اسم المستودع: ").strip()
            if not repo_name:
                print("❌ يجب إدخال اسم المستودع")
                continue

            description = input("أدخل وصف المشروع (اختياري): ").strip()

            private_input = input("هل تريد مستودع خاص؟ (y/n): ").strip().lower()
            private = private_input in ['y', 'yes', 'نعم']

            commit_message = input("رسالة الـ commit (اختياري): ").strip()
            if not commit_message:
                commit_message = "Initial commit"

            # رفع المشروع
            success = uploader.upload_project(
                project_path, repo_name, description, private, commit_message
            )

            if success:
                print("✅ تم رفع المشروع بنجاح!")
            else:
                print("❌ فشل في رفع المشروع")

        elif choice == "2":
            # إنشاء مستودع فقط
            repo_name = input("أدخل اسم المستودع: ").strip()
            if not repo_name:
                print("❌ يجب إدخال اسم المستودع")
                continue

            description = input("أدخل وصف المستودع (اختياري): ").strip()

            private_input = input("هل تريد مستودع خاص؟ (y/n): ").strip().lower()
            private = private_input in ['y', 'yes', 'نعم']

            auto_init_input = input("إنشاء README تلقائياً؟ (y/n): ").strip().lower()
            auto_init = auto_init_input in ['y', 'yes', 'نعم']

            # إنشاء المستودع
            repo_data = uploader.create_repository(repo_name, description, private, auto_init)

            if repo_data:
                print(f"✅ تم إنشاء المستودع بنجاح!")
                print(f"🔗 رابط المستودع: {repo_data['html_url']}")
                print(f"📋 رابط الاستنساخ: {repo_data['clone_url']}")
            else:
                print("❌ فشل في إنشاء المستودع")

        elif choice == "3":
            print("👋 شكراً لاستخدام الأداة!")
            break

        else:
            print("❌ اختيار غير صحيح، حاول مرة أخرى")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الأداة")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
