# GitHub Uploader Tool Docker Image
# أداة رفع المشاريع على GitHub

FROM python:3.9-slim

# معلومات الصورة
LABEL maintainer="GitHub Uploader Team"
LABEL description="أداة شاملة لرفع المشاريع على GitHub بشكل تلقائي"
LABEL version="1.0.0"

# تثبيت Git وأدوات النظام المطلوبة
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مستخدم غير root للأمان
RUN useradd -m -s /bin/bash uploader

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات أولاً للاستفادة من Docker cache
COPY requirements.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات المشروع
COPY github_uploader.py .
COPY config.py .
COPY examples.py .
COPY settings.json .

# نسخ ملفات التوثيق
COPY README.md .
COPY QUICK_START.md .
COPY LICENSE .

# إنشاء مجلد للمشاريع المحلية
RUN mkdir -p /app/projects

# تغيير ملكية الملفات للمستخدم uploader
RUN chown -R uploader:uploader /app

# التبديل للمستخدم غير root
USER uploader

# إعداد Git للمستخدم (سيتم تخصيصه عند التشغيل)
RUN git config --global init.defaultBranch main

# متغيرات البيئة
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# المنفذ (إذا كنا سنضيف واجهة ويب لاحقاً)
EXPOSE 8080

# نقطة الدخول
ENTRYPOINT ["python", "github_uploader.py"]

# الأمر الافتراضي
CMD ["--help"]
