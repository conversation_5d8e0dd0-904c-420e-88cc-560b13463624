version: '3.8'

services:
  github-uploader:
    build: .
    image: github-uploader:latest
    container_name: github-uploader-tool
    
    # ربط مجلدات المشاريع المحلية
    volumes:
      - ./projects:/app/projects:ro  # للقراءة فقط
      - ./output:/app/output         # لحفظ النتائج
      - ~/.gitconfig:/home/<USER>/.gitconfig:ro  # إعدادات Git
    
    # متغيرات البيئة
    environment:
      - GITHUB_TOKEN=${GITHUB_TOKEN}  # من ملف .env
      - LANGUAGE=ar
      - AUTO_CREATE_GITIGNORE=true
      - AUTO_CREATE_README=true
    
    # الشبكة
    networks:
      - uploader-network
    
    # إعادة التشغيل
    restart: unless-stopped
    
    # الأمر التفاعلي
    stdin_open: true
    tty: true

  # خدمة اختيارية لواجهة ويب (للمستقبل)
  web-interface:
    build: 
      context: .
      dockerfile: Dockerfile.web
    image: github-uploader-web:latest
    container_name: github-uploader-web
    
    ports:
      - "8080:8080"
    
    depends_on:
      - github-uploader
    
    environment:
      - API_URL=http://github-uploader:8080
    
    networks:
      - uploader-network
    
    # تعطيل هذه الخدمة افتراضياً
    profiles:
      - web

networks:
  uploader-network:
    driver: bridge

volumes:
  uploader-data:
    driver: local
