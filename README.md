# 🚀 GitHub Project Uploader - أداة رفع المشاريع على GitHub

أداة شاملة لرفع المشاريع على GitHub بشكل تلقائي مع دعم أنواع مختلفة من المشاريع.

## ✨ المميزات

- 🔄 **رفع تلقائي**: رفع المشاريع على GitHub بضغطة واحدة
- 🎯 **تحديد نوع المشروع**: تحديد تلقائي لنوع المشروع (Python, JavaScript, Java, إلخ)
- 📝 **إنشاء ملفات تلقائية**: إنشاء README.md و .gitignore تلقائياً
- 🔒 **دعم المستودعات الخاصة**: إمكانية إنشاء مستودعات خاصة أو عامة
- 🌍 **واجهة عربية**: واجهة مستخدم باللغة العربية
- 🛠️ **سهولة الاستخدام**: واجهة تفاعلية بسيطة

## 📋 المتطلبات

- Python 3.6+
- Git مثبت على النظام
- GitHub Personal Access Token

## 🔧 التثبيت

1. **استنساخ المشروع:**
```bash
git clone https://github.com/yourusername/github-uploader.git
cd github-uploader
```

2. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

## 🔑 إعداد GitHub Token

1. اذهب إلى [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
2. انقر على "Generate new token"
3. اختر الصلاحيات التالية:
   - `repo` (للوصول الكامل للمستودعات)
   - `user` (لقراءة معلومات المستخدم)
4. انسخ الـ token واحتفظ به في مكان آمن

## 🚀 الاستخدام

### تشغيل الأداة:
```bash
python github_uploader.py
```

### الخيارات المتاحة:

#### 1. رفع مشروع جديد
- رفع مشروع كامل من مجلد محلي إلى GitHub
- إنشاء README.md و .gitignore تلقائياً
- تهيئة Git وربطه بـ GitHub

#### 2. إنشاء مستودع فقط
- إنشاء مستودع فارغ على GitHub
- إمكانية إنشاء README تلقائياً

## 🎯 أنواع المشاريع المدعومة

الأداة تتعرف تلقائياً على الأنواع التالية:

- **Python** 🐍: ملفات .py أو requirements.txt
- **JavaScript/Node.js** 🟨: package.json أو ملفات .js
- **Java** ☕: ملفات .java أو pom.xml
- **C++** ⚡: ملفات .cpp أو .hpp
- **C#** 🔷: ملفات .cs أو .csproj
- **Go** 🐹: ملفات .go أو go.mod
- **Rust** 🦀: Cargo.toml

## 📁 هيكل ملفات .gitignore

الأداة تنشئ ملف .gitignore مناسب لكل نوع مشروع:

- **Python**: تجاهل __pycache__, .env, venv, إلخ
- **JavaScript**: تجاهل node_modules, .env, logs, إلخ
- **عام**: تجاهل ملفات النظام والـ IDE

## 🔧 استخدام الأداة برمجياً

```python
from github_uploader import GitHubUploader

# إنشاء كائن الأداة
uploader = GitHubUploader("your_github_token")

# رفع مشروع
success = uploader.upload_project(
    project_path="/path/to/your/project",
    repo_name="my-awesome-project",
    description="وصف المشروع",
    private=False,
    commit_message="Initial commit"
)

if success:
    print("تم رفع المشروع بنجاح!")
```

## 🛠️ الوظائف المتاحة

### `GitHubUploader` Class

- `create_repository()`: إنشاء مستودع جديد
- `detect_project_type()`: تحديد نوع المشروع
- `generate_gitignore()`: إنشاء ملف .gitignore
- `generate_readme()`: إنشاء ملف README.md
- `upload_project()`: رفع مشروع كامل

## 🐛 استكشاف الأخطاء

### خطأ في التوثيق:
- تأكد من صحة GitHub Token
- تأكد من وجود الصلاحيات المطلوبة

### خطأ في Git:
- تأكد من تثبيت Git على النظام
- تأكد من إعداد Git config (name & email)

### خطأ في المسار:
- تأكد من صحة مسار المشروع
- تأكد من وجود ملفات في المجلد

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات:

- افتح [Issue جديد](https://github.com/yourusername/github-uploader/issues)
- راسلنا على: <EMAIL>

## 🙏 شكر خاص

شكر خاص لجميع المساهمين في تطوير هذه الأداة.

---

**صنع بـ ❤️ للمطورين العرب**
